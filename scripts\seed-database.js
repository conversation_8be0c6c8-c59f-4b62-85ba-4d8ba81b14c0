#!/usr/bin/env node

/**
 * Database Seeding Script
 *
 * Run this script to seed your Appwrite database with dummy data
 * Usage: node scripts/seed-database.js
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🌱 Starting database seeding process...\n');

try {
    // Change to project root directory
    process.chdir(path.join(__dirname, '..'));

    console.log('🚀 Running TypeScript seed file directly...');

    // Use ts-node to run the TypeScript seed file directly
    try {
        execSync('npx ts-node lib/seed.ts', { stdio: 'inherit' });
    } catch (tsError) {
        console.log('📦 ts-node not available, trying with tsx...');
        try {
            execSync('npx tsx lib/seed.ts', { stdio: 'inherit' });
        } catch (tsxError) {
            console.log('📦 tsx not available, compiling with tsc...');

            // Compile TypeScript to JavaScript
            execSync('npx tsc lib/seed.ts --target es2020 --module commonjs --moduleResolution node --esModuleInterop --allowSyntheticDefaultImports --skipLibCheck', { stdio: 'inherit' });

            // Run the compiled JavaScript
            execSync('node lib/seed.js', { stdio: 'inherit' });

            // Clean up compiled file
            require('fs').unlinkSync('lib/seed.js');
        }
    }

} catch (error) {
    console.error('❌ Seeding failed:', error.message);

    console.log('\n📋 Alternative: Run seed directly in your app');
    console.log('1. Add this to any component or page:');
    console.log('   import seed from "@/lib/seed";');
    console.log('   seed().then(() => console.log("Seeding complete"));');
    console.log('');
    console.log('2. Or create a simple seed button in your app');
    console.log('3. Make sure your .env file has correct Appwrite credentials');

    process.exit(1);
}
