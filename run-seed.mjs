#!/usr/bin/env node

/**
 * Direct Database Seeding Script
 * 
 * This script directly imports and runs the seed function
 * Usage: node run-seed.mjs
 */

import seed from './lib/seed.js';

console.log('🌱 Starting database seeding...');
console.log('');
console.log('📊 This will populate your Appwrite database with:');
console.log('   ✅ 6 food categories (Burgers, Pizzas, Burritos, Sandwiches, Wraps, Bowls)');
console.log('   ✅ 10 customization options (toppings like Extra Cheese, Bacon, Avocado)');
console.log('   ✅ 10 side options (Coke, Fries, Garlic Bread, etc.)');
console.log('   ✅ 14 menu items with images, prices, and nutritional info');
console.log('');
console.log('⚠️  WARNING: This will clear existing data in your collections!');
console.log('');

try {
    await seed();
    
    console.log('');
    console.log('🎉 Database seeding completed successfully!');
    console.log('');
    console.log('📱 Next steps:');
    console.log('   1. Start your app: npm start');
    console.log('   2. Navigate to the Search tab to see all menu items');
    console.log('   3. Try filtering by categories (Burgers, Pizzas, etc.)');
    console.log('   4. Test adding items to cart');
    console.log('   5. Check the customization options when adding items');
    console.log('');
    console.log('🔍 What was seeded:');
    console.log('   • Categories: Burgers, Pizzas, Burritos, Sandwiches, Wraps, Bowls');
    console.log('   • Menu Items: Classic Cheeseburger, Pepperoni Pizza, Bean Burrito, and more');
    console.log('   • Customizations: Extra Cheese, Jalapeños, Bacon, Avocado, Fries, Coke, etc.');
    
} catch (error) {
    console.error('');
    console.error('❌ Database seeding failed!');
    console.error('Error:', error.message);
    console.error('');
    console.error('🔧 Troubleshooting steps:');
    console.error('   1. Check your .env file has correct Appwrite credentials:');
    console.error('      EXPO_PUBLIC_APPWRITE_PROJECT_ID=your_project_id');
    console.error('      EXPO_PUBLIC_APPWRITE_ENDPOINT=your_endpoint');
    console.error('');
    console.error('   2. Verify your Appwrite collections exist with correct IDs:');
    console.error('      - categories: 6893b91a00384571b2fe');
    console.error('      - menu: 6893bb5b00029f863d98');
    console.error('      - customizations: 6893bd110021f53b1f04');
    console.error('      - menuCustomizations: (check your appwrite.ts file)');
    console.error('');
    console.error('   3. Check your Appwrite project permissions');
    console.error('   4. Ensure your Appwrite project is active and not suspended');
    console.error('   5. Verify network connectivity to Appwrite servers');
    console.error('');
    console.error('   If the issue persists, check the Appwrite console for more details.');
    
    process.exit(1);
}
