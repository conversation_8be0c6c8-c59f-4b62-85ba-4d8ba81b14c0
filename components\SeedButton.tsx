import React, { useState } from 'react';
import { View, Text, Alert } from 'react-native';
import CustomButton from './CustomButton';
import seed from '@/lib/seed';

const SeedButton = () => {
    const [isSeeding, setIsSeeding] = useState(false);

    const handleSeed = async () => {
        Alert.alert(
            'Seed Database',
            'This will clear existing data and populate the database with sample food items. Continue?',
            [
                {
                    text: 'Cancel',
                    style: 'cancel',
                },
                {
                    text: 'Seed Database',
                    style: 'destructive',
                    onPress: async () => {
                        setIsSeeding(true);
                        try {
                            console.log('🌱 Starting database seeding...');
                            await seed();
                            console.log('✅ Seeding completed successfully!');
                            Alert.alert(
                                'Success!',
                                'Database has been seeded with sample data. You can now browse menu items in the Search tab.',
                                [{ text: 'OK' }]
                            );
                        } catch (error: any) {
                            console.error('❌ Seeding failed:', error);
                            Alert.alert(
                                'Seeding Failed',
                                `Error: ${error.message}\n\nCheck your Appwrite configuration and try again.`,
                                [{ text: 'OK' }]
                            );
                        } finally {
                            setIsSeeding(false);
                        }
                    },
                },
            ]
        );
    };

    return (
        <View className="p-5">
            <Text className="paragraph-bold text-dark-100 mb-3 text-center">
                Database Seeding
            </Text>
            <Text className="body-regular text-gray-200 mb-5 text-center">
                Populate your database with sample food items, categories, and customizations.
            </Text>
            <CustomButton
                title={isSeeding ? "Seeding Database..." : "Seed Database"}
                onPress={handleSeed}
                isLoading={isSeeding}
                style="bg-primary"
            />
            {isSeeding && (
                <Text className="small-bold text-primary mt-2 text-center">
                    This may take a few moments...
                </Text>
            )}
        </View>
    );
};

export default SeedButton;
